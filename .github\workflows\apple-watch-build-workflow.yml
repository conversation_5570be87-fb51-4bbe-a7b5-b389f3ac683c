# Dr. <PERSON><PERSON>cle Apple Watch Build & Deploy Workflow
# Builds and deploys the standalone Dr. Muscle Watch app to TestFlight
# Integrated with existing CI/CD patterns and Scaleway Mac infrastructure

name: Apple Watch Build & Deploy

concurrency:
  group: watch-build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  issues: write

on:
  workflow_dispatch: # Manual trigger
  push:
    branches:
      - Development_Watch_Carl_v1.1
    paths:
      - 'DrMuscleWatch/**'
      - '.github/workflows/apple-watch-build-workflow.yml'
  pull_request:
    branches:
      - Development_Watch_Carl_v1.1
    paths:
      - 'DrMuscleWatch/**'
      - '.github/workflows/apple-watch-build-workflow.yml'

# Dr. Muscle Watch App Configuration
env:
  # Project Configuration
  PROJECT_NAME: "DrMuscleWatchApp.xcodeproj"
  SCHEME_NAME: "DrMuscleWatchApp"
  APP_NAME: "DrMuscleWatchApp"
  PROJECT_PATH: "DrMuscleWatch/v1/DrM<PERSON><PERSON>WatchApp"

  # Bundle Identifiers (matching Apple Developer Portal configuration)
  WATCH_APP_BUNDLE_ID: "com.drmaxmuscle.max.watchkitapp"

  # Provisioning Profile Names (standalone watchOS app - no extension needed)
  WATCH_APP_PROVISIONING_PROFILE_NAME: "Dr_Muscle_Watch_App_Store_Profile"

jobs:
  # Centralized setup job for shared dependencies and version generation
  setup:
    name: Setup & Version Generation
    runs-on: ubicloud-standard-2
    outputs:
      start-time: ${{ steps.start-time.outputs.start-time }}
      version-code: ${{ steps.version.outputs.version_code }}
      version-name: ${{ steps.version.outputs.version_name }}
      assembly-version: ${{ steps.version.outputs.assembly_version }}
    steps:
    - name: Record workflow start time
      id: start-time
      run: |
        START_TIME=$(date +%s)
        echo "start-time=$START_TIME" >> $GITHUB_OUTPUT
        echo "Workflow started at: $(date -d @$START_TIME)"

    - uses: actions/checkout@v4

    - name: Generate unified version for Watch App
      id: version
      run: |
        # Get current date components for clean versioning
        YEAR=$(date +%Y)
        MONTH=$(date +%m)
        DAY=$(date +%d)
        BUILD_NUMBER=$GITHUB_RUN_NUMBER

        # Create clean 3-part version: 3.YYMM.DDXX (XX = last 2 digits of run)
        YY=$(echo $YEAR | tail -c 3)  # Last 2 digits of year (25 for 2025)
        YYMM="${YY}$(printf "%02d" $((10#$MONTH)))"  # YYMM format (2506 for June 2025)
        RUN_LAST_TWO=$(printf "%02d" $((BUILD_NUMBER % 100)))  # Last 2 digits of run (12 for run 112)
        DD_XX="$(printf "%02d" $((10#$DAY)))${RUN_LAST_TWO}"  # DDXX (1612 for 16th day, run 112)

        # Single clean 3-part version for Watch app
        CLEAN_VERSION="3.${YYMM}.${DD_XX}"

        echo "version_code=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "version_name=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "assembly_version=$CLEAN_VERSION" >> $GITHUB_OUTPUT

        echo "Using clean 3-part version format for Watch app:"
        echo "  Version: $CLEAN_VERSION (3.YYMM.DDXX where XX = last 2 digits of run)"
        echo "  Example: 3.2506.1212 = June 12th, 2025, run #112 → XX=12"

  # Check if runner is already available before waking up server
  check-runner-availability:
    name: Check Runner Availability
    runs-on: ubicloud-standard-2
    needs: setup
    outputs:
      runner-available: ${{ steps.check-runner-status.outputs.runner-available }}
      skip-wake-up: ${{ steps.check-runner-status.outputs.skip-wake-up }}
    steps:
    - name: Check if runner is already available
      id: check-runner-status
      run: |
        echo "🔍 Checking if Scaleway runner is already available..."

        # Install sshpass for SSH connectivity check
        sudo apt-get update -qq && sudo apt-get install -y sshpass

        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10"

        # Check if SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "⚠️ SSH credentials not available - will perform full wake-up"
          echo "runner-available=false" >> $GITHUB_OUTPUT
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        else
          # Quick check if runner process is already running
          echo "🔍 Checking if runner process is active..."
          RUNNER_CHECK=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "ps aux | grep 'Runner.Listener' | grep -v grep" 2>/dev/null || echo "No runner process found")

          if echo "$RUNNER_CHECK" | grep -q "Runner.Listener"; then
            echo "✅ Runner process is already running - skipping wake-up and SSH setup"
            echo "Runner process: $RUNNER_CHECK"
            echo "runner-available=true" >> $GITHUB_OUTPUT
            echo "skip-wake-up=true" >> $GITHUB_OUTPUT
          else
            echo "⚠️ Runner process not found - will perform wake-up and SSH setup"
            echo "runner-available=false" >> $GITHUB_OUTPUT
            echo "skip-wake-up=false" >> $GITHUB_OUTPUT
          fi
        fi

  # Wake up Scaleway Mac mini only if runner is not already available
  wake-scaleway-runner:
    name: Wake Up Scaleway Mac Runner
    if: always()
    runs-on: ubicloud-standard-2
    needs: [setup, check-runner-availability]
    outputs:
      runner-status: ${{ steps.check-runner.outputs.runner-status }}
    steps:
    - name: Check if wake-up is needed
      id: wake-up-check
      run: |
        if [ "${{ needs.check-runner-availability.outputs.skip-wake-up }}" = "true" ]; then
          echo "✅ Runner is already available - skipping wake-up process"
          echo "skip-wake-up=true" >> $GITHUB_OUTPUT
        else
          echo "🔄 Runner not available - proceeding with wake-up process"
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        fi

    - name: Wake up Scaleway Mac mini
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      env:
        SCW_ACCESS_KEY: ${{ secrets.SCW_ACCESS_KEY }}
        SCW_SECRET_KEY: ${{ secrets.SCW_SECRET_KEY }}
        SCW_DEFAULT_PROJECT_ID: ${{ secrets.SCW_DEFAULT_PROJECT_ID }}
        SCW_DEFAULT_ORGANIZATION_ID: ${{ secrets.SCW_DEFAULT_ORGANIZATION_ID }}
        SCW_DEFAULT_REGION: ${{ secrets.SCW_DEFAULT_REGION }}
        SCW_DEFAULT_ZONE: ${{ secrets.SCW_DEFAULT_ZONE }}
        SCALEWAY_SERVER_ID: ${{ secrets.SCALEWAY_SERVER_ID }}
      run: |
        echo "🚀 Attempting to wake up Scaleway Mac mini for Watch app build..."

        # Set default zone for Apple Silicon API calls
        if [ -n "$SCW_DEFAULT_ZONE" ]; then
          API_ZONE="$SCW_DEFAULT_ZONE"
          echo "✅ Using zone: $API_ZONE"
        else
          API_ZONE="fr-par-3"
          echo "✅ Using default zone: $API_ZONE"
        fi

        # Set Apple Silicon API endpoint
        API_ENDPOINT="https://api.scaleway.com/apple-silicon/v1alpha1/zones/$API_ZONE"
        echo "🔧 Using Apple Silicon API endpoint: $API_ENDPOINT"

        # Check current server status
        echo "🔍 Checking Apple Silicon Mac mini status..."
        SERVER_RESPONSE=$(curl -s -H "X-Auth-Token: $SCW_SECRET_KEY" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT/servers/$SCALEWAY_SERVER_ID")

        if echo "$SERVER_RESPONSE" | jq -e '.status' >/dev/null 2>&1; then
          SERVER_STATUS=$(echo "$SERVER_RESPONSE" | jq -r '.status')
          SERVER_NAME=$(echo "$SERVER_RESPONSE" | jq -r '.name')
          echo "✅ Found Apple Silicon server '$SERVER_NAME' with status: $SERVER_STATUS"
        else
          echo "❌ Failed to get server status:"
          echo "$SERVER_RESPONSE"
          exit 1
        fi

        echo "📊 Current server status: $SERVER_STATUS"

        # Skip reboot if server is already ready
        if [ "$SERVER_STATUS" = "ready" ]; then
          echo "✅ Server is already ready, skipping reboot to avoid service disruption"
        else
          echo "⚠️ Server not ready (status: $SERVER_STATUS), but skipping reboot for now"
          echo "ℹ️ If runner issues persist, manual reboot may be needed"
        fi

    - name: Wait for GitHub Actions runner to come online
      id: check-runner
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      run: |
        echo "⏳ Waiting for GitHub Actions runner to come online..."
        echo "ℹ️ Ensuring runner service is properly configured and running"

        # Server should already be ready, minimal wait for SSH
        echo "⏱️ Waiting 10 seconds for SSH to be ready..."
        sleep 10

        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=30"

        # Validate SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "❌ SSH credentials not configured in GitHub secrets"
          echo "runner-status=ssh-unavailable" >> $GITHUB_OUTPUT
          exit 0
        fi

        # Install sshpass for password authentication
        echo "📦 Installing sshpass for SSH password authentication..."
        sudo apt-get update -qq && sudo apt-get install -y sshpass

        # Check if runner service is running and fix if needed
        echo "🔍 Checking GitHub Actions runner service status..."
        SERVICE_STATUS=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && ./svc.sh status" 2>/dev/null || echo "FAILED")

        if echo "$SERVICE_STATUS" | grep -q "Started\|Running"; then
          echo "✅ Runner service is already running"
        else
          echo "⚠️ Runner service not running, starting runner directly..."
          # Start runner directly in background
          sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && nohup ./run.sh > runner.log 2>&1 &"
          sleep 15
        fi

        echo "✅ Wake-up and runner configuration completed"
        echo "runner-status=wake-completed" >> $GITHUB_OUTPUT

  build-watch-app:
    name: Watch App Build (Scaleway Mac)
    runs-on: [self-hosted, macos]
    needs: [setup, check-runner-availability, wake-scaleway-runner]
    outputs:
      watch-start-time: ${{ steps.watch-start-time.outputs.watch-start-time }}
      build-start-time: ${{ steps.build_watch.outputs.build-start-time }}
      build-end-time: ${{ steps.build_watch.outputs.build-end-time }}
      build-duration: ${{ steps.build_watch.outputs.build-duration }}
      build-success: ${{ steps.build_watch.outputs.build-success }}
      ipa-found: ${{ steps.check_ipa.outputs.ipa-found }}
      testflight-upload-success: ${{ steps.upload_testflight.outputs.upload-success }}
      version-code: ${{ needs.setup.outputs.version-code }}

    steps:
    - name: WORKFLOW VERSION CHECK - This confirms our changes are active
      run: |
        echo "🔍 WORKFLOW VERSION CHECK - v1.1 with SDK installation fixes"
        echo "This step confirms that our updated workflow is running"
        echo "Branch: ${{ github.ref }}"
        echo "Commit: ${{ github.sha }}"
        echo "Workflow file should include Pre-SDK and SDK installation steps"
        echo "✅ Workflow version check completed"

    - name: Record Watch app job start time
      id: watch-start-time
      run: |
        WATCH_START_TIME=$(date +%s)
        echo "watch-start-time=$WATCH_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build started at: $(date -r $WATCH_START_TIME)"

    - uses: actions/checkout@v4

    - name: Debug repository structure after checkout
      run: |
        echo "🔍 Debugging repository structure after checkout..."
        echo "Current working directory: $(pwd)"
        echo ""
        echo "Root directory contents:"
        ls -la
        echo ""
        echo "DrMuscleWatch directory structure (if exists):"
        if [ -d "DrMuscleWatch" ]; then
          find DrMuscleWatch -type f -name "*.xcodeproj" -o -name "Info.plist" | head -20
          echo ""
          echo "DrMuscleWatch directory tree (first 3 levels):"
          find DrMuscleWatch -maxdepth 3 -type d | sort
        else
          echo "❌ DrMuscleWatch directory does not exist!"
        fi
        echo ""
        echo "Looking for any .xcodeproj files in repository:"
        find . -name "*.xcodeproj" -type d | head -10

    - name: Validate Watch app project structure
      run: |
        echo "🔍 Validating Dr. Muscle Watch app project structure..."

        # Check if the project directory exists
        if [ ! -d "$PROJECT_PATH" ]; then
          echo "❌ Project directory not found: $PROJECT_PATH"
          exit 1
        fi

        # Check if the Xcode project exists (xcodeproj is a directory bundle)
        if [ ! -d "$PROJECT_PATH/$PROJECT_NAME" ]; then
          echo "❌ Xcode project not found: $PROJECT_PATH/$PROJECT_NAME"
          exit 1
        fi

        # Check if Info.plist exists
        INFO_PLIST_PATH="$PROJECT_PATH/DrMuscleWatchApp/Info.plist"
        if [ ! -f "$INFO_PLIST_PATH" ]; then
          echo "❌ Info.plist not found: $INFO_PLIST_PATH"
          exit 1
        fi

        echo "✅ Watch app project structure validated successfully"
        echo "  Project: $PROJECT_PATH/$PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Info.plist: $INFO_PLIST_PATH"

    - name: Set App Version in Info.plist
      run: |
        # Use version from setup job
        CLEAN_VERSION="${{ needs.setup.outputs.version-code }}"

        echo "🔧 Setting Watch app version: $CLEAN_VERSION"

        # Update Info.plist for the standalone Watch app
        INFO_PLIST_PATH="$PROJECT_PATH/DrMuscleWatchApp/Info.plist"
        if [ -f "$INFO_PLIST_PATH" ]; then
          echo "Found Info.plist at: $INFO_PLIST_PATH"

          # Show current content before update
          echo "Current version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH" || echo "No version keys found"

          # Create backup
          cp "$INFO_PLIST_PATH" "${INFO_PLIST_PATH}.bak"

          # Update both CFBundleShortVersionString and CFBundleVersion with same 3-part version
          plutil -replace CFBundleShortVersionString -string "$CLEAN_VERSION" "$INFO_PLIST_PATH"
          plutil -replace CFBundleVersion -string "$CLEAN_VERSION" "$INFO_PLIST_PATH"

          echo "✅ Successfully updated $INFO_PLIST_PATH"
          echo "Updated version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH"

          # Verify the update was successful
          if grep -q "$CLEAN_VERSION" "$INFO_PLIST_PATH"; then
            echo "✅ Version update verification successful"
          else
            echo "❌ Version update verification failed!"
            exit 1
          fi
        else
          echo "❌ Watch app Info.plist not found at $INFO_PLIST_PATH"
          exit 1
        fi

    - name: Clean up existing keychain
      run: |
        # Remove existing signing keychain if it exists (for self-hosted runners)
        if security list-keychains | grep -q "signing_temp.keychain"; then
          security delete-keychain signing_temp.keychain
          echo "🧹 Removed existing signing_temp.keychain"
        else
          echo "✅ No existing signing keychain to clean up"
        fi

        # Also check for keychain files in the filesystem
        if [ -f "$HOME/Library/Keychains/signing_temp.keychain-db" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain-db"
          echo "🧹 Removed signing_temp.keychain-db file"
        fi

        if [ -f "$HOME/Library/Keychains/signing_temp.keychain" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain"
          echo "🧹 Removed signing_temp.keychain file"
        fi

    - name: Install Apple Intermediate Certificate
      run: |
        # Download and install Apple Intermediate Certificate Authority for complete certificate chain
        echo "📥 Downloading Apple Intermediate Certificate Authority..."
        curl -o AppleWWDRCAG3.cer "https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer"

        # Import intermediate certificate into login keychain (no sudo required)
        echo "🔧 Importing intermediate certificate into login keychain..."
        if security import AppleWWDRCAG3.cer -k login.keychain -T /usr/bin/codesign; then
          echo "✅ Apple Intermediate Certificate Authority imported successfully"
        else
          echo "ℹ️ Certificate already exists in keychain (this is fine)"
        fi

        echo "✅ Apple Intermediate Certificate Authority is available in login keychain"

    - name: Import Code-Signing Certificates
      uses: apple-actions/import-codesign-certs@v3
      with:
        p12-file-base64: "${{ secrets.P12_CERTIFICATE }}"
        p12-password: "${{ secrets.P12_CERTIFICATE_PASSWORD }}"
        keychain-password: ""
        create-keychain: true

    - name: Verify certificate import
      run: |
        echo "🔍 Verifying imported certificates for watchOS..."
        echo "📋 All certificates in keychain:"
        security find-identity -v
        echo ""
        echo "🔐 Code signing certificates specifically:"
        security find-identity -v -p codesigning
        echo ""
        echo "🔍 Checking if certificate import succeeded..."
        CERT_COUNT=$(security find-identity -v -p codesigning | grep -c "Apple Distribution" || echo "0")
        if [ "$CERT_COUNT" = "0" ]; then
          echo "❌ No code signing certificates found! Certificate import may have failed."
          security find-identity -v
          exit 1
        else
          echo "✅ Found code signing certificates in keychain"
        fi

    - name: Validate Apple Team ID
      id: validate-team-id
      run: |
        # Check if APPLE_TEAM_ID secret is set and not empty
        if [ -z "${{ secrets.APPLE_TEAM_ID }}" ]; then
          echo "❌ APPLE_TEAM_ID secret is not set or empty"
          echo "ℹ️ Using fallback team ID from MAUI workflow: 7AAXZ47995"
          echo "apple-team-id=7AAXZ47995" >> $GITHUB_OUTPUT
        else
          echo "✅ APPLE_TEAM_ID secret is configured"
          echo "apple-team-id=${{ secrets.APPLE_TEAM_ID }}" >> $GITHUB_OUTPUT
        fi

    - name: Setup watchOS Provisioning Profile
      id: setup-provisioning
      run: |
        # Clean up any existing profiles
        echo "🧹 Cleaning up existing provisioning profiles..."
        rm -rf ~/Library/MobileDevice/Provisioning\ Profiles/
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/

        # Install watchOS-specific provisioning profile
        if [ -n "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" ]; then
          echo "📝 Installing watchOS App Store provisioning profile..."
          echo "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision

          if [ -f ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision ]; then
            PROFILE_SIZE=$(stat -f%z ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision)
            echo "✅ watchOS provisioning profile installed (${PROFILE_SIZE} bytes)"

            # Debug: Show profile contents for troubleshooting
            echo "🔍 Debugging provisioning profile contents..."

            # Check if this is a PKCS#7 signed profile (most common format)
            echo "🔍 Checking profile format..."
            FIRST_BYTES=$(head -c 4 ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision | xxd -p)
            echo "First 4 bytes: $FIRST_BYTES"

            if [[ "$FIRST_BYTES" == "3082"* ]]; then
              echo "📝 Profile is in PKCS#7 format (signed) - extracting plist content..."

              # Extract the plist content from the PKCS#7 container
              if security cms -D -i ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision > /tmp/extracted_profile.plist 2>/dev/null; then
                echo "✅ Successfully extracted plist from PKCS#7 container"

                # Validate the extracted plist
                if plutil -lint /tmp/extracted_profile.plist >/dev/null 2>&1; then
                  echo "✅ Extracted plist is valid"

                  # Check key profile details
                  echo "🔍 Profile validation details:"
                  PROFILE_NAME=$(plutil -extract Name raw /tmp/extracted_profile.plist 2>/dev/null || echo "Unknown")
                  BUNDLE_ID=$(plutil -extract Entitlements.application-identifier raw /tmp/extracted_profile.plist 2>/dev/null | sed 's/^[^.]*\.//' || echo "Unknown")
                  TEAM_ID=$(plutil -extract TeamIdentifier.0 raw /tmp/extracted_profile.plist 2>/dev/null || echo "Unknown")
                  PROFILE_UUID=$(plutil -extract UUID raw /tmp/extracted_profile.plist 2>/dev/null || echo "Unknown")

                  echo "  Profile Name: $PROFILE_NAME"
                  echo "  Bundle ID: $BUNDLE_ID"
                  echo "  Team ID: $TEAM_ID"
                  echo "  Profile UUID: $PROFILE_UUID"
                  echo "  Expected Bundle ID: ${{ env.WATCH_APP_BUNDLE_ID }}"
                  echo "  Expected Profile Name: ${{ env.WATCH_APP_PROVISIONING_PROFILE_NAME }}"

                  # Validate bundle ID matches
                  if [[ "$BUNDLE_ID" == "${{ env.WATCH_APP_BUNDLE_ID }}" ]]; then
                    echo "✅ Bundle ID matches expected value"
                    echo "profile-valid=true" >> $GITHUB_OUTPUT
                    echo "profile-uuid=$PROFILE_UUID" >> $GITHUB_OUTPUT
                  else
                    echo "❌ Bundle ID mismatch: expected '${{ env.WATCH_APP_BUNDLE_ID }}', got '$BUNDLE_ID'"
                    echo "profile-valid=false" >> $GITHUB_OUTPUT
                  fi
                else
                  echo "❌ Extracted plist is not valid"
                  echo "profile-valid=false" >> $GITHUB_OUTPUT
                fi

                # Clean up temp file
                rm -f /tmp/extracted_profile.plist
              else
                echo "❌ Failed to extract plist from PKCS#7 container"
                echo "🔍 First 100 bytes of file:"
                head -c 100 ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision | xxd
                echo "profile-valid=false" >> $GITHUB_OUTPUT
              fi
            else
              echo "� Profile appears to be raw plist format"
              # Try to read as raw plist
              if plutil -lint ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision >/dev/null 2>&1; then
                echo "✅ Raw plist validation passed"
                echo "profile-valid=true" >> $GITHUB_OUTPUT
              else
                echo "❌ Raw plist validation failed"
                echo "🔍 First 100 bytes of file:"
                head -c 100 ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision | xxd
                echo "profile-valid=false" >> $GITHUB_OUTPUT
              fi
            fi
          else
            echo "❌ Failed to install watchOS provisioning profile"
            echo "profile-valid=false" >> $GITHUB_OUTPUT
          fi
        else
          echo "❌ WATCH_APP_PROVISIONING_PROFILE_BASE64 secret not found!"
          echo "ℹ️ Please add the Dr_Muscle_Watch_App_Store_Profile as base64 to GitHub secrets"
          echo "profile-valid=false" >> $GITHUB_OUTPUT
        fi

        # List installed profiles for debugging
        echo "📋 Installed provisioning profiles:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/ || echo "No profiles directory found"

    - name: Pre-SDK Installation Diagnostics
      run: |
        echo "🔍 PRE-SDK DIAGNOSTICS - This step should always run"
        echo "Current working directory: $(pwd)"
        echo "Environment variables:"
        echo "  PROJECT_PATH: $PROJECT_PATH"
        echo "  PROJECT_NAME: $PROJECT_NAME"
        echo "  SCHEME_NAME: $SCHEME_NAME"
        echo "User: $(whoami)"
        echo "Date: $(date)"
        echo "✅ Pre-SDK diagnostics completed"

    - name: Force Install watchOS SDK
      run: |
        echo "🔧 FORCE INSTALLING WATCHOS SDK"
        echo "Xcode version: $(xcodebuild -version)"
        echo "Xcode path: $(xcode-select -p)"

        echo "📋 Currently installed SDKs:"
        xcodebuild -showsdks

        # Check if destinations work for our project
        echo "🔍 Testing destinations for our project..."
        cd "$PROJECT_PATH"
        DEST_OUTPUT=$(xcodebuild -showdestinations -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}" 2>&1)
        echo "Destinations output:"
        echo "$DEST_OUTPUT"

        if echo "$DEST_OUTPUT" | grep -q "watchOS 11.4 is not installed"; then
          echo "❌ watchOS SDK not properly installed - forcing installation"

          echo "🔄 Attempting to install watchOS platform..."
          xcodebuild -downloadPlatform watchOS || echo "⚠️ downloadPlatform failed"

          echo "🔄 Running first launch..."
          xcodebuild -runFirstLaunch || echo "⚠️ runFirstLaunch failed"

          echo "⏳ Waiting 60 seconds for installation to complete..."
          sleep 60

          echo "📋 SDKs after installation:"
          xcodebuild -showsdks

          echo "🔍 Testing destinations again..."
          DEST_OUTPUT_AFTER=$(xcodebuild -showdestinations -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}" 2>&1)
          echo "Destinations output after installation:"
          echo "$DEST_OUTPUT_AFTER"

          if echo "$DEST_OUTPUT_AFTER" | grep -q "watchOS 11.4 is not installed"; then
            echo "❌ watchOS SDK installation failed"
            echo "🔍 Checking platform directory:"
            ls -la /Applications/Xcode.app/Contents/Developer/Platforms/ | grep -i watch || echo "No watchOS platform found"
            exit 1
          else
            echo "✅ watchOS SDK installation successful"
          fi
        else
          echo "✅ watchOS SDK already properly installed"
        fi

    - name: Install watchOS SDK and Simulators
      run: |
        echo "� SDK INSTALLATION STEP STARTED"
        echo "Xcode version: $(xcodebuild -version)"
        echo "Xcode path: $(xcode-select -p)"

        echo "📋 Currently installed SDKs:"
        xcodebuild -showsdks

        echo "� Checking for watchOS SDK..."
        if xcodebuild -showsdks | grep -i watchos; then
          echo "✅ watchOS SDK found"
        else
          echo "❌ watchOS SDK not found"

          echo "� Attempting to install watchOS SDK..."
          echo "🔄 Trying: xcodebuild -downloadPlatform watchOS"
          xcodebuild -downloadPlatform watchOS || echo "⚠️ downloadPlatform failed"

          echo "� Trying: xcodebuild -runFirstLaunch"
          xcodebuild -runFirstLaunch || echo "⚠️ runFirstLaunch failed"

          echo "⏳ Waiting 15 seconds for installation..."
          sleep 15

          echo "📋 SDKs after installation attempt:"
          xcodebuild -showsdks
        fi

        echo "🔍 Checking simulator runtimes:"
        xcrun simctl list runtimes | grep -i watchos || echo "No watchOS runtimes found"

        echo "🔍 Checking available simulators:"
        xcrun simctl list devices | grep -i watch || echo "No Watch simulators found"

        echo "✅ SDK installation step completed"

    - name: Create ExportOptions.plist for watchOS export
      run: |
        # Change to project directory to create ExportOptions.plist where it will be used
        cd "$PROJECT_PATH"

        echo "📝 Creating ExportOptions.plist for watchOS app with automatic signing"

        cat << EOF > ExportOptions.plist
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>teamID</key>
            <string>${{ steps.validate-team-id.outputs.apple-team-id }}</string>
            <key>uploadSymbols</key>
            <true/>
            <key>signingStyle</key>
            <string>automatic</string>
        </dict>
        </plist>
        EOF
        echo "✅ ExportOptions.plist created for watchOS app with automatic signing"
        cat ExportOptions.plist

    - name: Clean up duplicate files and build Watch App
      id: build_watch
      run: |
        # Record build start time
        BUILD_START_TIME=$(date +%s)
        echo "build-start-time=$BUILD_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build compilation started at: $(date -r $BUILD_START_TIME)"

        # Change to project directory
        cd "$PROJECT_PATH"

        echo "🧹 Cleaning up duplicate files that may cause build conflicts..."
        # Remove duplicate files at root level (keep only the ones in DrMuscleWatchApp subdirectory)
        if [ -f "ContentView.swift" ]; then
          rm "ContentView.swift"
          echo "Removed duplicate ContentView.swift at root level"
        fi
        if [ -f "DrMuscleWatchApp.swift" ]; then
          rm "DrMuscleWatchApp.swift"
          echo "Removed duplicate DrMuscleWatchApp.swift at root level"
        fi

        echo "🧹 Cleaning Xcode build cache and derived data..."
        # Clean any existing build artifacts that might cause conflicts
        rm -rf build/
        rm -rf ~/Library/Developer/Xcode/DerivedData/DrMuscleWatchApp-*

        # Clean the project to ensure fresh build
        echo "🧹 Cleaning Xcode project..."
        xcodebuild clean -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}" -configuration Release || echo "⚠️ Clean command failed, continuing..."
        echo "� Starting Watch app build for simulator (no signing required)..."
        echo "  Project: $PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Configuration: Release"

        # Create build directory
        mkdir -p build

        # Setup signing configuration for device build
        APPLE_TEAM_ID="${{ steps.validate-team-id.outputs.apple-team-id }}"
        PROFILE_VALID="${{ steps.setup-provisioning.outputs.profile-valid }}"
        PROFILE_UUID="${{ steps.setup-provisioning.outputs.profile-uuid }}"
        echo "📝 Team ID: $APPLE_TEAM_ID"
        echo "📝 Profile valid: $PROFILE_VALID"
        echo "📝 Profile UUID: $PROFILE_UUID"

        # Build for device to create IPA for TestFlight using automatic signing
        echo "📝 Using automatic signing for watchOS app"
        echo "🎯 Building watchOS app for device with automatic signing..."

        if xcodebuild archive \
          -project "${PROJECT_NAME}" \
          -scheme "${SCHEME_NAME}" \
          -configuration Release \
          -destination "generic/platform=watchOS" \
          -archivePath "build/${APP_NAME}.xcarchive" \
          -allowProvisioningUpdates \
          CODE_SIGN_STYLE="Automatic" \
          DEVELOPMENT_TEAM="$APPLE_TEAM_ID" \
          PRODUCT_BUNDLE_IDENTIFIER="${WATCH_APP_BUNDLE_ID}" \
          ARCHS="arm64_32" \
          VALID_ARCHS="arm64_32" \
          ONLY_ACTIVE_ARCH=NO 2>&1; then
          echo "✅ Archive successful with automatic signing"
        else
          echo "❌ Automatic signing archive failed"
          exit 1
        fi

        # Record build end time and calculate duration
        BUILD_END_TIME=$(date +%s)
        echo "build-end-time=$BUILD_END_TIME" >> $GITHUB_OUTPUT
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        echo "build-duration=$BUILD_DURATION" >> $GITHUB_OUTPUT
        echo "Watch app build compilation completed at: $(date -r $BUILD_END_TIME)"
        echo "Build duration: $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s"

        # Mark build as successful
        echo "build-success=true" >> $GITHUB_OUTPUT
        echo "✅ Build completed successfully"

    - name: Export IPA for TestFlight
      id: check_ipa
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        echo "📦 Exporting IPA from archive for TestFlight upload..."

        # Check if archive exists
        if [ ! -d "build/${APP_NAME}.xcarchive" ]; then
          echo "❌ Archive not found: build/${APP_NAME}.xcarchive"
          exit 1
        fi

        # Create export directory
        mkdir -p build/ipa

        # Export IPA using the ExportOptions.plist
        echo "🔄 Exporting IPA with app-store method..."
        if xcodebuild -exportArchive \
          -archivePath "build/${APP_NAME}.xcarchive" \
          -exportPath "build/ipa" \
          -exportOptionsPlist "ExportOptions.plist" 2>&1; then
          echo "✅ IPA export successful"

          # Find the exported IPA
          IPA_PATH=$(find build/ipa -name "*.ipa" | head -n 1)
          if [ -n "$IPA_PATH" ] && [ -f "$IPA_PATH" ]; then
            echo "✅ IPA file created: $IPA_PATH"
            echo "ipa-found=true" >> $GITHUB_OUTPUT
            echo "ipa-path=$IPA_PATH" >> $GITHUB_OUTPUT

            # Get file size
            IPA_SIZE=$(stat -f%z "$IPA_PATH")
            IPA_SIZE_MB=$(echo "scale=2; $IPA_SIZE / 1024 / 1024" | bc -l)
            echo "📦 IPA Size: ${IPA_SIZE_MB} MB (${IPA_SIZE} bytes)"
          else
            echo "❌ IPA file not found after export"
            echo "ipa-found=false" >> $GITHUB_OUTPUT
            exit 1
          fi
        else
          echo "❌ IPA export failed"
          echo "ipa-found=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Verify IPA file for upload
      id: check_ipa_real
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        # Get IPA path from previous step
        IPA_PATH="${{ steps.check_ipa.outputs.ipa-path }}"

        if [ -n "$IPA_PATH" ] && [ -f "$IPA_PATH" ]; then
          echo "✅ IPA file verified for upload: $IPA_PATH"
          echo "ipa-found=true" >> $GITHUB_OUTPUT
          echo "ipa-path=$IPA_PATH" >> $GITHUB_OUTPUT

          # Get file size for final verification
          IPA_SIZE=$(stat -f%z "$IPA_PATH")
          IPA_SIZE_MB=$(echo "scale=2; $IPA_SIZE / 1024 / 1024" | bc -l)
          echo "📦 Final IPA Size: ${IPA_SIZE_MB} MB (${IPA_SIZE} bytes)"

          # Verify IPA is not corrupted
          if unzip -t "$IPA_PATH" >/dev/null 2>&1; then
            echo "✅ IPA file integrity verified"
          else
            echo "❌ IPA file appears to be corrupted"
            exit 1
          fi
        else
          echo "❌ IPA file is missing or invalid!"
          echo "ipa-found=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Install App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo -n "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" | base64 --decode --output ~/private_keys/AuthKey_${{ secrets.APPSTORE_API_KEY_ID }}.p8
        echo "✅ App Store Connect API Key installed."

    - name: Upload to TestFlight
      id: upload_testflight
      env:
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
        API_ISSUER: ${{ secrets.APPSTORE_ISSUER_ID }}
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        IPA_PATH="${{ steps.check_ipa_real.outputs.ipa-path }}"
        echo "🚀 Uploading Watch app to TestFlight..."
        echo "  IPA: $IPA_PATH"

        if xcrun altool --upload-app -f "$IPA_PATH" -t watchos --apiKey $API_KEY --apiIssuer $API_ISSUER; then
          echo "upload-success=true" >> $GITHUB_OUTPUT
          echo "✅ Successfully uploaded to TestFlight"
        else
          echo "upload-success=false" >> $GITHUB_OUTPUT
          echo "❌ Failed to upload to TestFlight"
          exit 1
        fi

    # Output Watch app build summary
    - name: Output Watch App Build Summary
      if: always()
      run: |
        echo "### ⌚ Dr. Muscle Watch App Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Project:** $PROJECT_PATH/$PROJECT_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Scheme:** $SCHEME_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Bundle ID:** $WATCH_APP_BUNDLE_ID" >> $GITHUB_STEP_SUMMARY

        # Add performance metrics
        if [ -n "${{ steps.build_watch.outputs.build-duration }}" ]; then
          BUILD_DURATION="${{ steps.build_watch.outputs.build-duration }}"
          echo "- **Build Duration:** $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add build status information
        if [ "${{ steps.build_watch.outputs.build-success }}" = "true" ]; then
          echo "- **Build:** ✅ Completed successfully" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Build:** ❌ Failed" >> $GITHUB_STEP_SUMMARY
        fi

        # Add package information if found
        if [ "${{ steps.check_ipa.outputs.ipa-found }}" = "true" ]; then
          echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Package Path:** \`${{ steps.check_ipa.outputs.ipa-path }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Version:** ${{ needs.setup.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **watchOS Target:** watchOS 9.0+" >> $GITHUB_STEP_SUMMARY

          # Add deployment information
          if [ "${{ steps.upload_testflight.outputs.upload-success }}" = "true" ]; then
            echo "- **Deployment:** ✅ Uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "- **Distribution:** TestFlight (Internal Testing)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Deployment:** ⚠️ Not uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "  - ℹ️ Reason: Upload failed or credentials missing" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Package:** ❌ Build failed, no package was generated" >> $GITHUB_STEP_SUMMARY
        fi

        # Add workflow timing
        if [ -n "${{ needs.setup.outputs.start-time }}" ] && [ -n "${{ steps.watch-start-time.outputs.watch-start-time }}" ]; then
          START_TIME="${{ needs.setup.outputs.start-time }}"
          CURRENT_TIME=$(date +%s)
          TOTAL_DURATION=$((CURRENT_TIME - START_TIME))
          echo "- **Total Workflow Runtime:** $(($TOTAL_DURATION / 60))m $(($TOTAL_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi