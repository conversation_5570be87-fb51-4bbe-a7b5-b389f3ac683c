// !$*UTF8*$!
{
    archiveVersion = 1;
    classes = {
    };
    objectVersion = 56;
    objects = {

/* Begin PBXBuildFile section */
        1A2B3C4D5E6F7890ABCDEF01 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF02 /* ContentView.swift */; };
        1A2B3C4D5E6F7890ABCDEF03 /* DrMuscleWatchApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF04 /* DrMuscleWatchApp.swift */; };
        1A2B3C4D5E6F7890ABCDEF05 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF06 /* Assets.xcassets */; };
        1A2B3C4D5E6F7890ABCDEF07 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF08 /* Preview Assets.xcassets */; };
        1A2B3C4D5E6F7890ABCDEF40 /* UserInfosModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF41 /* UserInfosModel.swift */; };
        1A2B3C4D5E6F7890ABCDEF42 /* AuthenticationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF43 /* AuthenticationManager.swift */; };
        1A2B3C4D5E6F7890ABCDEF44 /* PersistenceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF45 /* PersistenceController.swift */; };
        1A2B3C4D5E6F7890ABCDEF46 /* StorageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF47 /* StorageService.swift */; };
        1A2B3C4D5E6F7890ABCDEF48 /* LoginViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF49 /* LoginViewModel.swift */; };
        1A2B3C4D5E6F7890ABCDEF50 /* LoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF51 /* LoginView.swift */; };
        1A2B3C4D5E6F7890ABCDEF52 /* DrMuscleData.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF53 /* DrMuscleData.xcdatamodeld */; };
        1A2B3C4D5E6F7890ABCDEF70 /* Exercise+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF71 /* Exercise+CoreDataClass.swift */; };
        1A2B3C4D5E6F7890ABCDEF72 /* Exercise+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF73 /* Exercise+CoreDataProperties.swift */; };
        1A2B3C4D5E6F7890ABCDEF74 /* SetLog+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF75 /* SetLog+CoreDataClass.swift */; };
        1A2B3C4D5E6F7890ABCDEF76 /* SetLog+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF77 /* SetLog+CoreDataProperties.swift */; };
        1A2B3C4D5E6F7890ABCDEF78 /* Workout+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF79 /* Workout+CoreDataClass.swift */; };
        1A2B3C4D5E6F7890ABCDEF80 /* Workout+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF81 /* Workout+CoreDataProperties.swift */; };
        1A2B3C4D5E6F7890ABCDEF82 /* SyncService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCDEF83 /* SyncService.swift */; };

/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */

/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
        1A2B3C4D5E6F7890ABCDEF02 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF04 /* DrMuscleWatchApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DrMuscleWatchApp.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF06 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF08 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF13 /* DrMuscleWatchApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DrMuscleWatchApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
        1A2B3C4D5E6F7890ABCDEF14 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF41 /* UserInfosModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfosModel.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF43 /* AuthenticationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationManager.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF45 /* PersistenceController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersistenceController.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF47 /* StorageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StorageService.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF49 /* LoginViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewModel.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF51 /* LoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginView.swift; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF53 /* DrMuscleData.xcdatamodeld */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = DrMuscleData.xcdatamodeld; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF71 /* Exercise+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Exercise+CoreDataClass.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF73 /* Exercise+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Exercise+CoreDataProperties.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF75 /* SetLog+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SetLog+CoreDataClass.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF77 /* SetLog+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SetLog+CoreDataProperties.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF79 /* Workout+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Workout+CoreDataClass.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF81 /* Workout+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Workout+CoreDataProperties.swift"; sourceTree = "<group>"; };
        1A2B3C4D5E6F7890ABCDEF83 /* SyncService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyncService.swift; sourceTree = "<group>"; };

/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
        1A2B3C4D5E6F7890ABCDEF16 /* Frameworks */ = {
            isa = PBXFrameworksBuildPhase;
            buildActionMask = 2147483647;
            files = (
            );
            runOnlyForDeploymentPostprocessing = 0;
        };

/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
        1A2B3C4D5E6F7890ABCDEF18 = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF19 /* DrMuscleWatchApp */,
                1A2B3C4D5E6F7890ABCDEF21 /* Products */,
            );
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF21 /* Products */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF13 /* DrMuscleWatchApp.app */,
            );
            name = Products;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF19 /* DrMuscleWatchApp */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF04 /* DrMuscleWatchApp.swift */,
                1A2B3C4D5E6F7890ABCDEF02 /* ContentView.swift */,
                1A2B3C4D5E6F7890ABCDEF60 /* Models */,
                1A2B3C4D5E6F7890ABCDEF61 /* Services */,
                1A2B3C4D5E6F7890ABCDEF62 /* ViewModels */,
                1A2B3C4D5E6F7890ABCDEF63 /* Views */,
                1A2B3C4D5E6F7890ABCDEF53 /* DrMuscleData.xcdatamodeld */,
                1A2B3C4D5E6F7890ABCDEF06 /* Assets.xcassets */,
                1A2B3C4D5E6F7890ABCDEF14 /* Info.plist */,
                1A2B3C4D5E6F7890ABCDEF22 /* Preview Content */,
            );
            path = DrMuscleWatchApp;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF60 /* Models */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF41 /* UserInfosModel.swift */,
                1A2B3C4D5E6F7890ABCDEF71 /* Exercise+CoreDataClass.swift */,
                1A2B3C4D5E6F7890ABCDEF73 /* Exercise+CoreDataProperties.swift */,
                1A2B3C4D5E6F7890ABCDEF75 /* SetLog+CoreDataClass.swift */,
                1A2B3C4D5E6F7890ABCDEF77 /* SetLog+CoreDataProperties.swift */,
                1A2B3C4D5E6F7890ABCDEF79 /* Workout+CoreDataClass.swift */,
                1A2B3C4D5E6F7890ABCDEF81 /* Workout+CoreDataProperties.swift */,
            );
            path = Models;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF61 /* Services */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF43 /* AuthenticationManager.swift */,
                1A2B3C4D5E6F7890ABCDEF45 /* PersistenceController.swift */,
                1A2B3C4D5E6F7890ABCDEF47 /* StorageService.swift */,
                1A2B3C4D5E6F7890ABCDEF83 /* SyncService.swift */,
            );
            path = Services;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF62 /* ViewModels */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF49 /* LoginViewModel.swift */,
            );
            path = ViewModels;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF63 /* Views */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF51 /* LoginView.swift */,
            );
            path = Views;
            sourceTree = "<group>";
        };
        1A2B3C4D5E6F7890ABCDEF22 /* Preview Content */ = {
            isa = PBXGroup;
            children = (
                1A2B3C4D5E6F7890ABCDEF08 /* Preview Assets.xcassets */,
            );
            path = "Preview Content";
            sourceTree = "<group>";
        };

/* End PBXGroup section */

/* Begin PBXNativeTarget section */
        1A2B3C4D5E6F7890ABCDEF37 /* DrMuscleWatchApp */ = {
            isa = PBXNativeTarget;
            buildConfigurationList = 1A2B3C4D5E6F7890ABCDEF23 /* Build configuration list for PBXNativeTarget "DrMuscleWatchApp" */;
            buildPhases = (
                1A2B3C4D5E6F7890ABCDEF24 /* Sources */,
                1A2B3C4D5E6F7890ABCDEF16 /* Frameworks */,
                1A2B3C4D5E6F7890ABCDEF25 /* Resources */,
            );
            buildRules = (
            );
            dependencies = (
            );
            name = DrMuscleWatchApp;
            productName = DrMuscleWatchApp;
            productReference = 1A2B3C4D5E6F7890ABCDEF13 /* DrMuscleWatchApp.app */;
            productType = "com.apple.product-type.application";
        };

/* End PBXNativeTarget section */

/* Begin PBXProject section */
        1A2B3C4D5E6F7890ABCDEF12 /* Project object */ = {
            isa = PBXProject;
            attributes = {
                BuildIndependentTargetsInParallel = 0;
                LastSwiftUpdateCheck = 1500;
                LastUpgradeCheck = 1500;
                TargetAttributes = {
                    1A2B3C4D5E6F7890ABCDEF37 = {
                        CreatedOnToolsVersion = 15.0;
                    };
                };
            };
            buildConfigurationList = 1A2B3C4D5E6F7890ABCDEF30 /* Build configuration list for PBXProject "DrMuscleWatchApp" */;
            compatibilityVersion = "Xcode 14.0";
            developmentRegion = en;
            hasScannedForEncodings = 0;
            knownRegions = (
                en,
                Base,
            );
            mainGroup = 1A2B3C4D5E6F7890ABCDEF18;
            productRefGroup = 1A2B3C4D5E6F7890ABCDEF21 /* Products */;
            projectDirPath = "";
            projectRoot = "";
            targets = (
                1A2B3C4D5E6F7890ABCDEF37 /* DrMuscleWatchApp */,
            );
        };
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
        1A2B3C4D5E6F7890ABCDEF25 /* Resources */ = {
            isa = PBXResourcesBuildPhase;
            buildActionMask = 2147483647;
            files = (
                1A2B3C4D5E6F7890ABCDEF07 /* Preview Assets.xcassets in Resources */,
                1A2B3C4D5E6F7890ABCDEF05 /* Assets.xcassets in Resources */,
            );
            runOnlyForDeploymentPostprocessing = 0;
        };

/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
        1A2B3C4D5E6F7890ABCDEF24 /* Sources */ = {
            isa = PBXSourcesBuildPhase;
            buildActionMask = 2147483647;
            files = (
                1A2B3C4D5E6F7890ABCDEF01 /* ContentView.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF03 /* DrMuscleWatchApp.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF40 /* UserInfosModel.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF42 /* AuthenticationManager.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF44 /* PersistenceController.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF46 /* StorageService.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF48 /* LoginViewModel.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF50 /* LoginView.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF52 /* DrMuscleData.xcdatamodeld in Sources */,
                1A2B3C4D5E6F7890ABCDEF70 /* Exercise+CoreDataClass.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF72 /* Exercise+CoreDataProperties.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF74 /* SetLog+CoreDataClass.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF76 /* SetLog+CoreDataProperties.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF78 /* Workout+CoreDataClass.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF80 /* Workout+CoreDataProperties.swift in Sources */,
                1A2B3C4D5E6F7890ABCDEF82 /* SyncService.swift in Sources */,
            );
            runOnlyForDeploymentPostprocessing = 0;
        };

/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */

/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
        1A2B3C4D5E6F7890ABCDEF31 /* Debug */ = {
            isa = XCBuildConfiguration;
            buildSettings = {
                ALWAYS_SEARCH_USER_PATHS = NO;
                ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
                CLANG_ANALYZER_NONNULL = YES;
                CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
                CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
                CLANG_ENABLE_MODULES = YES;
                CLANG_ENABLE_OBJC_ARC = YES;
                CLANG_ENABLE_OBJC_WEAK = YES;
                CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
                CLANG_WARN_BOOL_CONVERSION = YES;
                CLANG_WARN_COMMA = YES;
                CLANG_WARN_CONSTANT_CONVERSION = YES;
                CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
                CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
                CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
                CLANG_WARN_EMPTY_BODY = YES;
                CLANG_WARN_ENUM_CONVERSION = YES;
                CLANG_WARN_INFINITE_RECURSION = YES;
                CLANG_WARN_INT_CONVERSION = YES;
                CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
                CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
                CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
                CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
                CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
                CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
                CLANG_WARN_STRICT_PROTOTYPES = YES;
                CLANG_WARN_SUSPICIOUS_MOVE = YES;
                CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
                CLANG_WARN_UNREACHABLE_CODE = YES;
                CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
                COPY_PHASE_STRIP = NO;
                DEBUG_INFORMATION_FORMAT = dwarf;
                ENABLE_STRICT_OBJC_MSGSEND = YES;
                ENABLE_TESTABILITY = YES;
                ENABLE_USER_SCRIPT_SANDBOXING = YES;
                GCC_C_LANGUAGE_STANDARD = gnu17;
                GCC_DYNAMIC_NO_PIC = NO;
                GCC_NO_COMMON_BLOCKS = YES;
                GCC_OPTIMIZATION_LEVEL = 0;
                GCC_PREPROCESSOR_DEFINITIONS = (
                    "DEBUG=1",
                    "$(inherited)",
                );
                GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
                GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
                GCC_WARN_UNDECLARED_SELECTOR = YES;
                GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
                GCC_WARN_UNUSED_FUNCTION = YES;
                GCC_WARN_UNUSED_VARIABLE = YES;
                MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
                MTL_FAST_MATH = YES;
                ONLY_ACTIVE_ARCH = YES;
                SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
                SWIFT_OPTIMIZATION_LEVEL = "-Onone";
            };
            name = Debug;
        };
        1A2B3C4D5E6F7890ABCDEF32 /* Release */ = {
            isa = XCBuildConfiguration;
            buildSettings = {
                ALWAYS_SEARCH_USER_PATHS = NO;
                ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
                CLANG_ANALYZER_NONNULL = YES;
                CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
                CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
                CLANG_ENABLE_MODULES = YES;
                CLANG_ENABLE_OBJC_ARC = YES;
                CLANG_ENABLE_OBJC_WEAK = YES;
                CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
                CLANG_WARN_BOOL_CONVERSION = YES;
                CLANG_WARN_COMMA = YES;
                CLANG_WARN_CONSTANT_CONVERSION = YES;
                CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
                CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
                CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
                CLANG_WARN_EMPTY_BODY = YES;
                CLANG_WARN_ENUM_CONVERSION = YES;
                CLANG_WARN_INFINITE_RECURSION = YES;
                CLANG_WARN_INT_CONVERSION = YES;
                CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
                CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
                CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
                CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
                CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
                CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
                CLANG_WARN_STRICT_PROTOTYPES = YES;
                CLANG_WARN_SUSPICIOUS_MOVE = YES;
                CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
                CLANG_WARN_UNREACHABLE_CODE = YES;
                CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
                COPY_PHASE_STRIP = NO;
                DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
                ENABLE_NS_ASSERTIONS = NO;
                ENABLE_STRICT_OBJC_MSGSEND = YES;
                ENABLE_USER_SCRIPT_SANDBOXING = YES;
                GCC_C_LANGUAGE_STANDARD = gnu17;
                GCC_NO_COMMON_BLOCKS = YES;
                GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
                GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
                GCC_WARN_UNDECLARED_SELECTOR = YES;
                GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
                GCC_WARN_UNUSED_FUNCTION = YES;
                GCC_WARN_UNUSED_VARIABLE = YES;
                MTL_ENABLE_DEBUG_INFO = NO;
                MTL_FAST_MATH = YES;
                SWIFT_COMPILATION_MODE = wholemodule;
            };
            name = Release;
        };
        1A2B3C4D5E6F7890ABCDEF33 /* Debug */ = {
            isa = XCBuildConfiguration;
            buildSettings = {
                ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
                ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
                CODE_SIGN_STYLE = Automatic;
                CURRENT_PROJECT_VERSION = 1;
                DEVELOPMENT_ASSET_PATHS = "\"DrMuscleWatchApp/Preview Content\"";
                ENABLE_BITCODE = NO;
                ENABLE_PREVIEWS = YES;
                GENERATE_INFOPLIST_FILE = YES;
                INFOPLIST_FILE = DrMuscleWatchApp/Info.plist;
                INFOPLIST_KEY_CFBundleDisplayName = "Dr. Muscle";
                INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
                INFOPLIST_KEY_WKWatchOnly = YES;
                LD_RUNPATH_SEARCH_PATHS = (
                    "$(inherited)",
                    "@executable_path/Frameworks",
                );
                MARKETING_VERSION = 1.0;
                PRODUCT_BUNDLE_IDENTIFIER = com.drmaxmuscle.dr_max_muscle.watchapp;
                PRODUCT_NAME = "$(TARGET_NAME)";
                SDKROOT = watchos;
                SKIP_INSTALL = YES;
                SWIFT_EMIT_LOC_STRINGS = YES;
                SWIFT_VERSION = 5.0;
                TARGETED_DEVICE_FAMILY = 4;
                WATCHOS_DEPLOYMENT_TARGET = 9.0;
            };
            name = Debug;
        };
        1A2B3C4D5E6F7890ABCDEF34 /* Release */ = {
            isa = XCBuildConfiguration;
            buildSettings = {
                ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
                ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
                CODE_SIGN_STYLE = Automatic;
                CURRENT_PROJECT_VERSION = 1;
                DEVELOPMENT_ASSET_PATHS = "\"DrMuscleWatchApp/Preview Content\"";
                ENABLE_BITCODE = NO;
                ENABLE_PREVIEWS = YES;
                GENERATE_INFOPLIST_FILE = YES;
                INFOPLIST_FILE = DrMuscleWatchApp/Info.plist;
                INFOPLIST_KEY_CFBundleDisplayName = "Dr. Muscle";
                INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
                INFOPLIST_KEY_WKWatchOnly = YES;
                LD_RUNPATH_SEARCH_PATHS = (
                    "$(inherited)",
                    "@executable_path/Frameworks",
                );
                MARKETING_VERSION = 1.0;
                PRODUCT_BUNDLE_IDENTIFIER = com.drmaxmuscle.dr_max_muscle.watchapp;
                PRODUCT_NAME = "$(TARGET_NAME)";
                SDKROOT = watchos;
                SKIP_INSTALL = YES;
                SWIFT_EMIT_LOC_STRINGS = YES;
                SWIFT_VERSION = 5.0;
                TARGETED_DEVICE_FAMILY = 4;
                WATCHOS_DEPLOYMENT_TARGET = 9.0;
            };
            name = Release;
        };

/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
        1A2B3C4D5E6F7890ABCDEF30 /* Build configuration list for PBXProject "DrMuscleWatchApp" */ = {
            isa = XCConfigurationList;
            buildConfigurations = (
                1A2B3C4D5E6F7890ABCDEF31 /* Debug */,
                1A2B3C4D5E6F7890ABCDEF32 /* Release */,
            );
            defaultConfigurationIsVisible = 0;
            defaultConfigurationName = Release;
        };
        1A2B3C4D5E6F7890ABCDEF23 /* Build configuration list for PBXNativeTarget "DrMuscleWatchApp" */ = {
            isa = XCConfigurationList;
            buildConfigurations = (
                1A2B3C4D5E6F7890ABCDEF33 /* Debug */,
                1A2B3C4D5E6F7890ABCDEF34 /* Release */,
            );
            defaultConfigurationIsVisible = 0;
            defaultConfigurationName = Release;
        };

/* End XCConfigurationList section */
    };
    rootObject = 1A2B3C4D5E6F7890ABCDEF12 /* Project object */;
}